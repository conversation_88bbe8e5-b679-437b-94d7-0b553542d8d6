/**
 * 관리자 리스트 페이지 공통 JavaScript 라이브러리
 * - AJAX 기반 검색/필터/정렬 처리
 * - 동적 테이블 렌더링
 * - 페이징 처리
 * - 디바운스 적용 실시간 검색
 */

class AdminListManager {
    constructor(config) {
        this.config = {
            // 기본 설정
            apiEndpoint: '/search', // 기본값, 각 페이지에서 오버라이드 필요
            tableSelector: '#dynamicTableContent',
            searchDelay: 500,
            pageSize: 20,
            
            // 콜백 함수들
            onDataLoaded: null,
            onError: null,
            onSearchStart: null,
            onSearchComplete: null,
            
            // 커스텀 렌더러
            customRowRenderer: null,
            customHeaderRenderer: null,
            
            // 설정 오버라이드
            ...config
        };
        
        this.currentSearchCondition = {
            page: 1,
            size: this.config.pageSize,
            sortField: 'create_date',
            sortDirection: 'DESC',
            searchKeyword: '',
            filters: {},
            dateFrom: null,
            dateTo: null,
            dateField: null,
            numberFrom: null,
            numberTo: null,
            numberField: null
        };
        
        this.isLoading = false;
        this.searchTimeout = null;
        
        this.init();
    }
    
    /**
     * 초기화
     */
    init() {
        this.bindEvents();
        this.loadInitialData();
    }
    
    /**
     * 이벤트 바인딩
     */
    bindEvents() {
        // 검색 버튼
        $(document).on('click', '#searchBtn', () => {
            this.updateSearchKeyword();
            this.performSearch();
        });
        
        // 검색 초기화 버튼
        $(document).on('click', '#clearSearchBtn', () => {
            this.clearSearch();
        });
        
        // 전체 초기화 버튼
        $(document).on('click', '#resetAllBtn', () => {
            this.resetAll();
        });
        
        // 검색어 입력 시 실시간 검색 제거 (버튼/엔터키만 사용)
        
        // 검색어 엔터키
        $(document).on('keypress', '#searchKeyword', (e) => {
            if (e.which === 13) {
                e.preventDefault();
                this.updateSearchKeyword();
                this.performSearch();
            }
        });
        
        // 필터 변경
        $(document).on('change', '.filter-select', (e) => {
            this.updateFilter(e.target.dataset.filterField, e.target.value);
            this.performSearch();
        });
        
        // 정렬 변경
        $(document).on('change', '#sortField, #sortDirection', () => {
            this.updateSort();
            this.performSearch();
        });
        
        // 페이지 크기 변경
        $(document).on('change', '#pageSize', (e) => {
            this.updatePageSize(parseInt(e.target.value));
            this.performSearch();
        });
        
        // 날짜 범위 변경
        $(document).on('change', '#dateFrom, #dateTo', () => {
            this.updateDateRange();
            this.performSearch();
        });
        
        // 숫자 범위 변경
        $(document).on('change', '#numberFrom, #numberTo', () => {
            this.updateNumberRange();
            this.performSearch();
        });
        
        // 페이징 클릭
        $(document).on('click', '.pagination .page-link', (e) => {
            e.preventDefault();
            const page = parseInt(e.target.dataset.page);
            if (page && page !== this.currentSearchCondition.page) {
                this.goToPage(page);
            }
        });
    }
    
    /**
     * 검색 키워드 업데이트 (버튼 클릭이나 엔터키 시에만 호출)
     */
    updateSearchKeyword() {
        const keyword = $('#searchKeyword').val().trim();
        this.currentSearchCondition.searchKeyword = keyword;
        this.currentSearchCondition.page = 1; // 검색 시 첫 페이지로
    }
    
    /**
     * 검색 실행
     */
    async performSearch() {
        if (this.isLoading) {
            return;
        }
        
        try {
            this.setLoading(true);
            
            // 검색 시작 콜백
            if (this.config.onSearchStart) {
                this.config.onSearchStart(this.currentSearchCondition);
            }
            
            // 검색 필드 설정
            const searchFieldsData = $('#searchFieldsData').val();
            if (searchFieldsData) {
                this.currentSearchCondition.searchFields = this.parseSearchFields(searchFieldsData);
            }
            
            // API 호출
            const response = await this.callApi(this.config.apiEndpoint, this.currentSearchCondition);
            
            if (response.success) {
                this.renderResults(response);
                
                // 데이터 로드 완료 콜백
                if (this.config.onDataLoaded) {
                    this.config.onDataLoaded(response);
                }
            } else {
                this.showError(response.message || '데이터 조회 중 오류가 발생했습니다.');
            }
            
        } catch (error) {
            console.error('Search error:', error);
            this.showError('서버 통신 중 오류가 발생했습니다.');
            
            // 에러 콜백
            if (this.config.onError) {
                this.config.onError(error);
            }
        } finally {
            this.setLoading(false);
            
            // 검색 완료 콜백
            if (this.config.onSearchComplete) {
                this.config.onSearchComplete(this.currentSearchCondition);
            }
        }
    }
    
    /**
     * API 호출
     */
    async callApi(endpoint, data) {
        const response = await $.ajax({
            url: endpoint,
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(data),
            beforeSend: function (xhr) { xhr.setRequestHeader( [[${_csrf.headerName}]], [[${_csrf.token}]] ); }
        });
        
        return response;
    }
    
    /**
     * 결과 렌더링
     */
    renderResults(response) {
        const $container = $(this.config.tableSelector);
        
        if (response.items && response.items.length > 0) {
            // 테이블 렌더링
            const tableHtml = this.renderTable(response.items, response);
            $container.html(tableHtml);
            
            // 페이징 정보 업데이트
            this.updatePaginationInfo(response);
            
            // 페이징 네비게이션 렌더링
            this.renderPagination(response);
            
        } else {
            // 빈 결과 표시
            $container.html(this.renderEmptyResult());
        }
        
        // 검색 결과 정보 업데이트
        this.updateSearchResultInfo(response);
    }
    
    /**
     * 테이블 렌더링
     */
    renderTable(items, response) {
        if (this.config.customRowRenderer) {
            return this.config.customRowRenderer(items, response);
        }
        
        // 기본 테이블 렌더링 (각 페이지에서 오버라이드 필요)
        let html = '<table class="table table-hover"><tbody>';
        items.forEach(item => {
            html += '<tr><td>' + JSON.stringify(item) + '</td></tr>';
        });
        html += '</tbody></table>';
        
        return html;
    }
    
    /**
     * 빈 결과 렌더링
     */
    renderEmptyResult() {
        return `
            <div class="text-center py-5">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">조회된 데이터가 없습니다</h5>
                <p class="text-muted">검색 조건을 변경하거나 새로운 데이터를 등록해보세요.</p>
            </div>
        `;
    }
    
    /**
     * 페이징 네비게이션 렌더링
     */
    renderPagination(response) {
        if (response.totalPages <= 1) {
            $('.admin-list-pagination').hide();
            return;
        }
        
        $('.admin-list-pagination').show();
        
        // 페이지 번호 업데이트
        $('.pagination .page-link').each(function() {
            const $link = $(this);
            const page = parseInt($link.data('page'));
            
            if (page === response.currentPage) {
                $link.closest('.page-item').addClass('active');
            } else {
                $link.closest('.page-item').removeClass('active');
            }
        });
    }
    
    /**
     * 검색 결과 정보 업데이트
     */
    updateSearchResultInfo(response) {
        $('.result-count').html(`
            전체 <strong>${response.totalCount}</strong>개 중 
            <strong>${response.startItemNumber}</strong> - 
            <strong>${response.endItemNumber}</strong>개 표시
        `);
        
        if (response.isSearchResult) {
            $('.search-indicator').show();
        } else {
            $('.search-indicator').hide();
        }
    }
    
    /**
     * 로딩 상태 설정
     */
    setLoading(loading) {
        this.isLoading = loading;
        
        if (loading) {
            $('#listLoadingIndicator').removeClass('d-none');
            $(this.config.tableSelector).addClass('opacity-50');
        } else {
            $('#listLoadingIndicator').addClass('d-none');
            $(this.config.tableSelector).removeClass('opacity-50');
        }
    }
    
    /**
     * 에러 표시
     */
    showError(message) {
        const errorHtml = `
            <div class="alert alert-danger text-center" role="alert">
                <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                <h5>데이터 로드 중 오류가 발생했습니다</h5>
                <p>${message}</p>
                <button type="button" class="btn btn-outline-danger" onclick="location.reload()">
                    <i class="fas fa-redo"></i> 페이지 새로고침
                </button>
            </div>
        `;
        
        $(this.config.tableSelector).html(errorHtml);
    }

    /**
     * 검색 초기화
     */
    clearSearch() {
        $('#searchKeyword').val('');
        this.currentSearchCondition.searchKeyword = '';
        this.currentSearchCondition.page = 1;
        this.performSearch();
    }

    /**
     * 전체 초기화
     */
    resetAll() {
        // 폼 초기화
        $('#searchKeyword').val('');
        $('.filter-select').val('');
        $('#dateFrom, #dateTo').val('');
        $('#numberFrom, #numberTo').val('');
        $('#sortField').val('create_date');
        $('#sortDirection').val('DESC');
        $('#pageSize').val('20');

        // 검색 조건 초기화
        this.currentSearchCondition = {
            page: 1,
            size: 20,
            sortField: 'create_date',
            sortDirection: 'DESC',
            searchKeyword: '',
            filters: {},
            dateFrom: null,
            dateTo: null,
            dateField: null,
            numberFrom: null,
            numberTo: null,
            numberField: null
        };

        this.performSearch();
    }

    /**
     * 필터 업데이트
     */
    updateFilter(field, value) {
        if (value && value.trim() !== '') {
            this.currentSearchCondition.filters[field] = value;
        } else {
            delete this.currentSearchCondition.filters[field];
        }
        this.currentSearchCondition.page = 1; // 필터 변경 시 첫 페이지로
    }

    /**
     * 정렬 업데이트
     */
    updateSort() {
        this.currentSearchCondition.sortField = $('#sortField').val();
        this.currentSearchCondition.sortDirection = $('#sortDirection').val();
        this.currentSearchCondition.page = 1; // 정렬 변경 시 첫 페이지로
    }

    /**
     * 페이지 크기 업데이트
     */
    updatePageSize(size) {
        this.currentSearchCondition.size = size;
        this.currentSearchCondition.page = 1; // 페이지 크기 변경 시 첫 페이지로
    }

    /**
     * 날짜 범위 업데이트
     */
    updateDateRange() {
        const dateFrom = $('#dateFrom').val();
        const dateTo = $('#dateTo').val();
        const dateField = $('#dateField').val();

        if (dateFrom && dateTo && dateField) {
            this.currentSearchCondition.dateFrom = dateFrom;
            this.currentSearchCondition.dateTo = dateTo;
            this.currentSearchCondition.dateField = dateField;
        } else {
            this.currentSearchCondition.dateFrom = null;
            this.currentSearchCondition.dateTo = null;
            this.currentSearchCondition.dateField = null;
        }
        this.currentSearchCondition.page = 1;
    }

    /**
     * 숫자 범위 업데이트
     */
    updateNumberRange() {
        const numberFrom = $('#numberFrom').val();
        const numberTo = $('#numberTo').val();
        const numberField = $('#numberField').val();

        if (numberFrom && numberTo && numberField) {
            this.currentSearchCondition.numberFrom = parseInt(numberFrom);
            this.currentSearchCondition.numberTo = parseInt(numberTo);
            this.currentSearchCondition.numberField = numberField;
        } else {
            this.currentSearchCondition.numberFrom = null;
            this.currentSearchCondition.numberTo = null;
            this.currentSearchCondition.numberField = null;
        }
        this.currentSearchCondition.page = 1;
    }

    /**
     * 페이지 이동
     */
    goToPage(page) {
        this.currentSearchCondition.page = page;
        $('#currentPage').val(page);
        this.performSearch();
    }

    /**
     * 검색 필드 파싱
     */
    parseSearchFields(searchFieldsData) {
        if (!searchFieldsData) return [];

        return searchFieldsData.split(',').map(field => {
            const parts = field.split(':');
            return parts[0].trim();
        });
    }

    /**
     * 페이징 정보 업데이트
     */
    updatePaginationInfo(response) {
        $('#currentPage').val(response.currentPage);
    }

    /**
     * 초기 데이터 로드
     */
    loadInitialData() {
        // URL 파라미터에서 검색 조건 복원
        this.restoreSearchConditionFromUrl();
        this.performSearch();
    }

    /**
     * URL에서 검색 조건 복원
     */
    restoreSearchConditionFromUrl() {
        const urlParams = new URLSearchParams(window.location.search);

        // 검색 키워드
        const keyword = urlParams.get('searchKeyword');
        if (keyword) {
            this.currentSearchCondition.searchKeyword = keyword;
            $('#searchKeyword').val(keyword);
        }

        // 페이지
        const page = urlParams.get('page');
        if (page) {
            this.currentSearchCondition.page = parseInt(page);
        }

        // 페이지 크기
        const size = urlParams.get('size');
        if (size) {
            this.currentSearchCondition.size = parseInt(size);
            $('#pageSize').val(size);
        }

        // 정렬
        const sortField = urlParams.get('sortField');
        const sortDirection = urlParams.get('sortDirection');
        if (sortField) {
            this.currentSearchCondition.sortField = sortField;
            $('#sortField').val(sortField);
        }
        if (sortDirection) {
            this.currentSearchCondition.sortDirection = sortDirection;
            $('#sortDirection').val(sortDirection);
        }

        // 필터들
        $('.filter-select').each((index, element) => {
            const field = element.dataset.filterField;
            const value = urlParams.get(`filter_${field}`);
            if (value) {
                this.currentSearchCondition.filters[field] = value;
                $(element).val(value);
            }
        });
    }

    /**
     * 현재 검색 조건을 URL에 반영
     */
    updateUrl() {
        const params = new URLSearchParams();

        if (this.currentSearchCondition.searchKeyword) {
            params.set('searchKeyword', this.currentSearchCondition.searchKeyword);
        }

        if (this.currentSearchCondition.page > 1) {
            params.set('page', this.currentSearchCondition.page);
        }

        if (this.currentSearchCondition.size !== 20) {
            params.set('size', this.currentSearchCondition.size);
        }

        if (this.currentSearchCondition.sortField !== 'create_date') {
            params.set('sortField', this.currentSearchCondition.sortField);
        }

        if (this.currentSearchCondition.sortDirection !== 'DESC') {
            params.set('sortDirection', this.currentSearchCondition.sortDirection);
        }

        // 필터들
        Object.keys(this.currentSearchCondition.filters).forEach(key => {
            params.set(`filter_${key}`, this.currentSearchCondition.filters[key]);
        });

        // URL 업데이트 (페이지 새로고침 없이)
        const newUrl = `${window.location.pathname}?${params.toString()}`;
        window.history.replaceState({}, '', newUrl);
    }

    /**
     * 외부에서 검색 조건 설정
     */
    setSearchCondition(condition) {
        this.currentSearchCondition = { ...this.currentSearchCondition, ...condition };
        this.performSearch();
    }

    /**
     * 현재 검색 조건 반환
     */
    getSearchCondition() {
        return { ...this.currentSearchCondition };
    }

    /**
     * 새로고침
     */
    refresh() {
        this.performSearch();
    }
}

// 전역 객체에 등록
window.AdminListManager = AdminListManager;
