/**
 * 관리자 리스트 페이지 공통 CSS
 * - 검색/필터/정렬 컨트롤 스타일링
 * - 반응형 디자인 (모바일/태블릿 대응)
 * - 일관된 UI/UX 제공
 */

/* ========== 기본 컨테이너 ========== */
.admin-list-controls {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.search-filter-section {
    margin-bottom: 1rem;
}

.search-result-info {
    padding: 1rem 0;
    border-top: 1px solid #e9ecef;
    margin-top: 1rem;
}

/* ========== 검색 입력 필드 ========== */
.admin-list-controls .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.admin-list-controls .form-control,
.admin-list-controls .form-select {
    background-color: #fff !important;
    color: #495057 !important;
    border: 1px solid #ced4da;
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.admin-list-controls .form-control:focus,
.admin-list-controls .form-select:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.admin-list-controls .input-group .btn {
    border: 1px solid #ced4da;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}

.admin-list-controls .input-group .btn:hover {
    background-color: #f8f9fa;
    border-color: #adb5bd;
}

/* ========== 검색 결과 정보 ========== */
.result-count {
    font-size: 0.875rem;
    color: #6c757d;
}

.result-count strong {
    color: #495057;
    font-weight: 600;
}

.search-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    color: #007bff;
    margin-left: 0.5rem;
}

/* ========== 로딩 인디케이터 ========== */
.loading-indicator {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    color: #6c757d;
}

.loading-indicator .spinner-border {
    width: 2rem;
    height: 2rem;
}

/* ========== 테이블 컨테이너 ========== */
.admin-list-table-container {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.admin-list-table-container .table {
    margin-bottom: 0;
}

.admin-list-table-container .table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    font-size: 0.875rem;
    padding: 0.75rem;
}

.admin-list-table-container .table td {
    padding: 0.75rem;
    vertical-align: middle;
    font-size: 0.875rem;
    border-bottom: 1px solid #dee2e6;
}

.admin-list-table-container .table tbody tr:hover {
    background-color: #f8f9fa;
}

/* ========== 페이징 네비게이션 ========== */
.admin-list-pagination {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.admin-list-pagination .pagination {
    margin-bottom: 0;
}

.admin-list-pagination .page-link {
    color: #495057;
    border: 1px solid #dee2e6;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}

.admin-list-pagination .page-link:hover {
    color: #007bff;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.admin-list-pagination .page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
    color: #fff;
}

.admin-list-pagination .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
}

/* ========== 빈 결과 및 에러 표시 ========== */
.admin-list-empty,
.admin-list-error {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 3rem 2rem;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.admin-list-empty i,
.admin-list-error i {
    color: #6c757d;
    margin-bottom: 1rem;
}

.admin-list-empty h5,
.admin-list-error h5 {
    color: #495057;
    margin-bottom: 0.5rem;
}

.admin-list-empty p,
.admin-list-error p {
    color: #6c757d;
    margin-bottom: 1rem;
}

/* ========== 버튼 스타일 ========== */
.admin-list-controls .btn {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.15s ease-in-out;
}

.admin-list-controls .btn-outline-secondary {
    color: #6c757d;
    border-color: #6c757d;
}

.admin-list-controls .btn-outline-secondary:hover {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

/* ========== 반응형 디자인 ========== */

/* 태블릿 (768px 이하) */
@media (max-width: 768px) {
    .admin-list-controls {
        padding: 1rem;
    }
    
    .admin-list-controls .row.g-3 {
        --bs-gutter-x: 0.75rem;
        --bs-gutter-y: 0.75rem;
    }
    
    .admin-list-controls .col-md-4,
    .admin-list-controls .col-md-3,
    .admin-list-controls .col-md-2 {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .search-result-info .row {
        text-align: center;
    }
    
    .search-result-info .col-md-6 {
        flex: 0 0 100%;
        max-width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .admin-list-table-container {
        overflow-x: auto;
    }
    
    .admin-list-table-container .table {
        min-width: 600px;
    }
    
    .admin-list-pagination .pagination {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .admin-list-pagination .page-item {
        margin: 0.125rem;
    }
}

/* 모바일 (576px 이하) */
@media (max-width: 576px) {
    .admin-list-controls {
        padding: 0.75rem;
        margin-bottom: 1rem;
    }
    
    .admin-list-controls .form-label {
        font-size: 0.8rem;
        margin-bottom: 0.25rem;
    }
    
    .admin-list-controls .form-control,
    .admin-list-controls .form-select {
        font-size: 0.8rem;
        padding: 0.375rem 0.5rem;
    }
    
    .admin-list-controls .btn {
        font-size: 0.8rem;
        padding: 0.375rem 0.75rem;
    }
    
    .search-result-info {
        padding: 0.75rem 0;
    }
    
    .result-count {
        font-size: 0.8rem;
    }
    
    .admin-list-table-container .table th,
    .admin-list-table-container .table td {
        padding: 0.5rem 0.375rem;
        font-size: 0.8rem;
    }
    
    .admin-list-pagination {
        padding: 0.75rem;
    }
    
    .admin-list-pagination .page-link {
        padding: 0.375rem 0.5rem;
        font-size: 0.8rem;
    }
    
    .admin-list-empty,
    .admin-list-error {
        padding: 2rem 1rem;
    }
    
    .admin-list-empty h5,
    .admin-list-error h5 {
        font-size: 1rem;
    }
    
    .admin-list-empty p,
    .admin-list-error p {
        font-size: 0.875rem;
    }
}

/* ========== 접근성 개선 ========== */
.admin-list-controls .form-control:focus,
.admin-list-controls .form-select:focus,
.admin-list-controls .btn:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

.admin-list-pagination .page-link:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* ========== 애니메이션 ========== */
.admin-list-table-container,
.admin-list-pagination,
.admin-list-empty,
.admin-list-error {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ========== 로딩 상태 ========== */
.opacity-50 {
    opacity: 0.5;
    pointer-events: none;
}

